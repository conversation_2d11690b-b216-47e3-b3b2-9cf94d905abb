# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**EverTrek** is a Joomla 3.x CMS-based travel/trekking holiday booking platform with custom components for managing holidays, accommodations, locations, and pricing. The site uses a decoupled architecture with an external checkout system.

## Development Environment

### Local Setup (DDEV)
```bash
# Start development environment
ddev start

# Access site
# URL: https://evertrek.ddev.site
# Admin: https://evertrek.ddev.site/administrator

# Stop environment
ddev stop

# Database snapshot (auto-runs on ddev stop via pre-stop hook)
ddev snapshot --name=$(date +%Y%m%d%H%M)

# Export database
ddev export-db --file=.tarballs/db.$(date +%Y%m%d%H%M).sql.gz

# SSH into container
ddev ssh
```

### Configuration
- **PHP**: 7.4
- **Database**: MariaDB 10.11
- **Webserver**: nginx-fpm
- **Base URL**: https://evertrek.ddev.site
- **DB Prefix**: `ev_`

### Joomla Configuration
- Config file: `configuration.php`
- Database: `db` (host: `db`, user: `db`, pass: `db`)
- Google Maps API key is set in config
- Debug mode: enabled in development

## Core Architecture

### Custom Components (com_zen*)

The "zen" namespace indicates custom TravelZen/EverTrek components:

1. **com_zenholidays** - Main holiday/trek management
   - Models: holiday, date, price, itinerary, version
   - Multi-currency pricing system
   - Holiday versioning with date ranges
   - Data feed export (JSON/XML)
   - PDF generation for holiday details

2. **com_zenlocations** - Location hierarchy (continents → countries)
   - Hierarchical location structure (level 1: regions, level 2: countries)
   - Links to holidays via `#__zenlocations_usage` table
   - Map integration and visualization

3. **com_zenaccommodations** - Accommodation listings
   - Search and filtering
   - Category-based organization

4. **com_zenactivities** - Activity management
   - Pricing models
   - Activity listings

5. **com_zenadmin** - Central admin/configuration
   - Client config API endpoint
   - Currency management (`#__zencurrencies`, `#__zencurrencyrates`)
   - System-wide settings

6. **com_zenprovider** - API provider for external systems
   - HMAC-SHA256 authentication
   - Product/booking API for external checkout
   - Package and booking reservation endpoints

7. **com_zenugc** - User-generated content
   - Reviews, testimonials, trip reports

8. **com_zenimages** - Image/album management

### Custom Libraries

**libraries/mrzen/** - Core utility library
- Classes: Utils, Cache, GeoIP integration (MaxMind)
- Helpers: HTML attributes, money/currency, session, geolocation
- Models: Base model classes
- PDF generation utilities
- CLI tools for maintenance
- Traits for shared behavior

**libraries/mzholidays/** - Holiday-specific library
- AvailabilityMode and DurationMode enums
- Holiday model traits
- Holiday-specific helpers

### Custom Template

**templates/zenbase/** - Main site template
- Custom template with SCSS compilation
- Mobile menu (mmenu) integration
- Custom helpers in `custom_helpers.php`
- Email templates in `email/` directory
- Extensive JavaScript in `js/` directory
- SPPB (SP Page Builder) integration

### Custom Plugins

**plugins/sppagebuilder/travelzen/** - SP Page Builder custom addons
- `accommodation` - Accommodation display addon
- `holiday` - Individual holiday addon
- `holidays` - Multiple holidays grid/list
- `holidaydates` - Date picker/display
- `customgmap` - Google Maps integration
- `searchfromprice` - Price-based search
- `ugc` - User-generated content display

**plugins/content/** - Content plugins for articles/pages

**plugins/holidays/** - Holiday-specific plugins

**plugins/ajax/** - AJAX handlers

**plugins/mrzen/** - Custom system plugins
- Forex pricing automation
- Custom functionality hooks

### Database Schema

#### Core Tables
- `ev_zenholidays` - Holiday/trek products
- `ev_zenholidayversions` - Holiday versions with dates
- `ev_zenholidayprices` - Multi-currency pricing
- `ev_zenlocations` - Location hierarchy
- `ev_zenlocations_usage` - Links locations to holidays
- `ev_zenaccommodations` - Accommodation listings
- `ev_zencurrencies` - Supported currencies
- `ev_zencurrencyrates` - Exchange rates with date ranges

#### Key Relationships
```
Continent (level 1) → Country (level 2) → Location Usage → Holiday Version → Holiday
```

## API Endpoints

### Public Endpoints (No Auth)
```bash
# Client configuration
/index.php?option=com_zenadmin&view=clientconfig&format=json

# Holiday by version code
/index.php?option=com_zenholidays&task=holiday.get_json&vc={code}

# Accommodation search
/index.php?option=com_zenaccommodations&task=search.results
```

### Authenticated Endpoints

**Holiday Data Feed (Access Key)**
```bash
curl -H "X-Access-Key: YOUR_KEY" \
  "/index.php?option=com_zenholidays&view=datafeed&format=json&id={id}"
```

**ZenProvider API (HMAC Signature)**
```bash
curl -H "X-Request-Signature: {public_key} {timestamp} SHA256:{signature}" \
  -H "Content-Type: application/json" \
  "/index.php?option=com_zenprovider&view=book&format=json"
```

See `docs/api-quick-reference.md` and `docs/api-endpoints-documentation.md` for full API documentation.

## Multi-Currency System

### Architecture
- **Base Currency**: USD (configurable in com_zenadmin)
- **Automatic Conversion**: Prices in base currency auto-convert to all active currencies
- **Exchange Rates**: Stored in `ev_zencurrencyrates` with date validity
- **Frontend Detection**: GeoIP-based currency detection with manual override
- **Currency Switcher**: `mod_zencurrencyswitcher` module

### Adding Currencies
1. Admin → Zen Admin → Currencies (add currency)
2. Admin → Zen Admin → Currency Rates (add exchange rates)
3. Plugin auto-generates prices when base currency changes
4. Currency appears in frontend switcher automatically

See `docs/product-pricing-system.md` for comprehensive currency documentation.

## Frontend JavaScript

### Main Libraries
- **TravelZen.js** (`libraries/mrzen/assets/js/`) - Core Angular.js application
  - Price filters and currency formatting
  - Search functionality
  - Angular directives and services

### Key Features
- Angular.js-based dynamic content
- Price formatting with currency conversion
- Deep linking to tabs and sections
- Elastic search integration for holidays
- Date/price popups and modals

## Common Development Tasks

### Working with Holidays

**Get a holiday model:**
```php
$holidayModel = JModelLegacy::getInstance('Holiday', 'ZenHolidaysModel');
$holiday = $holidayModel->getItem($id);
```

**Get holidays for a location:**
```php
$trips = $holidayModel->getTripsForLocation($countryId);
```

### Working with Locations

**Get location with hierarchy:**
```php
$locationModel = JModelLegacy::getInstance('Location', 'ZenLocationsModel');
$country = $locationModel->getItem($countryId);
```

### Working with Prices

**Prices are automatically generated** - edit base currency prices only:
1. Edit price in base currency (USD)
2. ForexPricing plugin detects change
3. Auto-generates prices in all active currencies using exchange rates
4. Applies currency-specific rounding rules

### SPPB Custom Addons

Located in `plugins/sppagebuilder/travelzen/addons/`

**Creating/modifying addons:**
- Each addon has: `addon.php`, `site.php`, `admin.php`
- Use SPPB field system for admin interface
- See `docs/sppb-field-system-analysis.md` for field documentation
- See `docs/sppagebuilder-guide.md` for addon development

### Testing

**Browser Testing:**
- Use Playwright or Chrome DevTools MCP tools for CSS/JS debugging
- Test currency switching across browsers
- Mobile responsive testing required

**Database Testing:**
```bash
# Access database
ddev mysql

# Query examples in docs/database.md
```

## Important Constraints

### DO NOT Modify
- Core Joomla files
- Third-party extensions (EasyBlog, SP Page Builder core, JCH Optimize, RSForm, etc.)
- Core zen component database schemas without migration
- External checkout system (different codebase)

### Template-Level Changes Only
- Currency display enhancements go in zenbase template
- JavaScript enhancements in template JS files
- CSS/SCSS in template styling

### Plugin Development
- System plugins must not interfere with existing zen functionality
- Use Joomla event system properly
- Document all plugin hooks and triggers

## Documentation

Project documentation is in `docs/` directory:

- `database.md` - Database schema and relationships
- `product-pricing-system.md` - Complete currency/pricing documentation
- `api-quick-reference.md` - Quick API reference
- `api-endpoints-documentation.md` - Detailed API docs
- `sppagebuilder-guide.md` - SP Page Builder usage
- `knowledge-centre-updated-proposal.md` - Recent Knowledge Centre project
- `currency-switching-project-plan.md` - Currency enhancement project

## Git Workflow

**Main branch:** `main`

**Current branch:** `20251006-knowledge-centre-revamp`

**Commit workflow:**
- Create feature branches from main
- Use descriptive commit messages
- Test thoroughly before committing
- Auto-snapshot on `ddev stop` (via pre-stop hook)

## Key MCP Tools

When the user reports CSS or JavaScript issues:
1. Use Playwright MCP tool to inspect the page
2. Use Chrome DevTools MCP tool for debugging
3. Step back through recent changes to identify breaking change

## Markdown Formatting Rules

- Headings must have a blank line after
- Indented bullets must be preceded by 4 spaces
- Bullet lists must have a blank line before (even after paragraphs)

## Agent Rules

From `.augment/rules/agents.md`:
- Use Playwright/Chrome DevTools MCP for CSS/JS debugging
- If something breaks, step back through changes to find responsible change
- Follow proper markdown formatting

## Architecture Patterns

### MVC Pattern
- Models in `models/`
- Views in `views/`
- Controllers in `controllers/`
- Use Joomla's MVC framework

### Traits for Shared Logic
- Holiday-related logic in `libraries/mzholidays/traits/`
- Shared model behavior in traits
- Include with `use TraitName;`

### Caching
- ZenCache plugin for locale-aware caching
- Cache keys include currency and language
- JCH Optimize for page-level caching

### External Integration
- ZenProvider API for external checkout
- HMAC-SHA256 authentication
- Product data sync via API or data feeds
- See `docs/product-pricing-system.md` section 5 for cart/checkout integration

## Holiday Template System

Holidays use a versioning system:
- Each holiday has multiple versions
- Versions have date ranges and prices
- Prices link to dates via `date_id`
- Each date can have multiple price types (adult, child, single supplement, etc.)

## Search and Filtering

- Elastic search integration for holidays
- Accommodation search with faceted filtering
- Location-based filtering using hierarchy

## Performance Considerations

- Multi-layer caching (ZenCache, JCH Optimize, CDN)
- Cache keys include: domain, path, currency, language, device type
- GeoIP detection for currency (MaxMind GeoLite2)
- Angular.js filters for client-side rendering

## Support Resources

- Joomla 3.x documentation: https://docs.joomla.org/
- DDEV documentation: https://ddev.readthedocs.io/
- Project docs in `docs/` directory
