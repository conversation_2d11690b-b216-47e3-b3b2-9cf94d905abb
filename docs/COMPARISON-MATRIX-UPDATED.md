# Comparison Matrix - Updated Configuration ✅

## Changes Made

### 1. Category Alias
- **Alias**: `comparison-matrix` ✅

### 2. Copy Item Title Clarified
- **Purpose**: Admin reference only (not displayed on website)
- **Format**: Brand name (e.g., "Kondor Adventures", "Intrepid Travel")
- **Website Display**: Brand name comes from JSON `competitors.name` field

### 3. Data Structure Clarified
- **Section Titles**: Generic (e.g., "Trip Inclusions", "Food", "Accommodation")
- **Feature Labels**: Trip-specific (customize for each holiday)
- **Feature Items**: Trip-specific (customize for each holiday)

## Updated Files

✅ `templates/zenbase/html/com_zenholidays/holiday/default_comparison-matrix.php`
- Uses category alias `comparison-matrix`

✅ `templates/zenbase/html/com_zenholidays/holiday/default_overview.php`
- Added include for comparison matrix template (line 670)

✅ All Documentation Files Updated:
- `COMPARISON-MATRIX-IMPLEMENTATION.md`
- `COMPARISON-MATRIX-SUMMARY.md`
- `COMPARISON-MATRIX-CHECKLIST.md`
- `templates/zenbase/html/com_zenholidays/holiday/COMPARISON-MATRIX-README.md`
- `templates/zenbase/html/com_zenholidays/holiday/COMPARISON-MATRIX-QUICK-REFERENCE.md`

## Your Next Steps

### Step 1: Create Category
- Title: `Comparison Matrix`
- Alias: `comparison-matrix`
- Extension: `com_zenholidays`

### Step 2: Create Copy Items (One Per Competitor)

For each competitor you want to compare:

1. Go to: **Components → Copy Items → Copy Items**
2. Click: **New**
3. Fill in:
   - **Title**: `[Brand Name]` (e.g., "Kondor Adventures")
     - This is for admin reference only
     - Not displayed on the website
   - **Category**: `Comparison Matrix`
   - **Content**: JSON data (see structure below)
   - **State**: Published

### Step 3: Link to Holiday

1. Edit the holiday (e.g., Everest Base Camp)
2. Go to **Copy Items** tab
3. Add the competitor copy items you created
4. Set state to **Published**
5. Save

## JSON Structure for Each Competitor

```json
{
  "title": "Why Choose EverTrek?",
  "evertrek": {
    "subtitle": "Premium EBC Specialist"
  },
  "competitors": {
    "kondor": {
      "name": "Kondor Adventures",
      "features": {
        "ebc-specialist": {
          "icon": "text",
          "text": "Kondor"
        },
        "pre-trip-guidance": {
          "icon": "check",
          "text": "Online resources and briefing documents"
        }
      }
    }
  },
  "features": [
    {
      "section_title": "Trip Inclusions",
      "items": {
        "ebc-specialist": {
          "label": "EBC Trek Specialist",
          "evertrek": {
            "icon": "star",
            "text": "Dedicated EBC specialist plus expert team"
          }
        },
        "pre-trip-guidance": {
          "label": "Pre-trip expert guidance",
          "evertrek": {
            "icon": "star",
            "text": "Live briefings, kit advice, training plans"
          }
        }
      }
    }
  ]
}
```

## Key Points

### Copy Item Title (Admin Only)
```
Title: "Kondor Adventures"
↓
Used in Joomla admin for organization
NOT displayed on website
```

### Brand Name on Website (From JSON)
```json
"competitors": {
  "kondor": {
    "name": "Kondor Adventures"  ← This is displayed on website
  }
}
```

### Section Titles (Generic)
```json
"section_title": "Trip Inclusions"  ← Generic, reusable
"section_title": "Food"             ← Generic, reusable
"section_title": "Accommodation"    ← Generic, reusable
```

### Feature Labels & Items (Trip-Specific)
```json
"ebc-specialist": {                    ← Trip-specific ID
  "label": "EBC Trek Specialist",     ← Trip-specific label
  "evertrek": {
    "icon": "star",
    "text": "Dedicated EBC specialist"  ← Trip-specific content
  }
}
```

## Example: Multiple Competitors for One Trip

### Copy Item 1
- **Title**: "Kondor Adventures" (admin reference)
- **Category**: Comparison Matrix
- **Content**: JSON with Kondor's data for EBC trip

### Copy Item 2
- **Title**: "Intrepid Travel" (admin reference)
- **Category**: Comparison Matrix
- **Content**: JSON with Intrepid's data for EBC trip

### Copy Item 3
- **Title**: "G Adventures" (admin reference)
- **Category**: Comparison Matrix
- **Content**: JSON with G Adventures' data for EBC trip

### Link All to Holiday
In the Everest Base Camp holiday:
- Link all 3 copy items
- Users can select any competitor from dropdown
- Each competitor's data is trip-specific

## Data Organization Strategy

### Option A: One Copy Item Per Competitor (Recommended)
```
Copy Item: "Kondor Adventures - EBC"
- Contains: All Kondor data for EBC trip
- Linked to: Everest Base Camp holiday

Copy Item: "Kondor Adventures - Kilimanjaro"
- Contains: All Kondor data for Kilimanjaro trip
- Linked to: Kilimanjaro holiday
```

**Pros**: 
- Easy to manage
- Clear organization
- Trip-specific data

### Option B: Multiple Competitors in One Copy Item
```
Copy Item: "EBC Competitors"
- Contains: Kondor, Intrepid, G Adventures data for EBC
- Linked to: Everest Base Camp holiday
```

**Pros**: 
- Fewer copy items
- All data in one place

**Cons**: 
- Larger JSON files
- Harder to update individual competitors

## Workflow Example

### For Everest Base Camp Trip:

1. **Research Competitors**
   - Kondor Adventures
   - Intrepid Travel
   - G Adventures

2. **Create Copy Items**
   - Title: "Kondor Adventures" → JSON with Kondor's EBC data
   - Title: "Intrepid Travel" → JSON with Intrepid's EBC data
   - Title: "G Adventures" → JSON with G Adventures' EBC data

3. **Customize Features for EBC**
   - Section: "Trip Inclusions"
     - Feature: "EBC Trek Specialist" (trip-specific)
     - Feature: "Pre-trip expert guidance" (trip-specific)
   - Section: "Food"
     - Feature: "Breakfast & Welcome Meals" (trip-specific)
     - Feature: "Purified water via filters" (trip-specific)

4. **Link to Holiday**
   - Edit Everest Base Camp holiday
   - Add all 3 competitor copy items
   - Publish

5. **Result**
   - Users see comparison matrix on EBC page
   - Can select any of 3 competitors
   - All data is EBC-specific

## Testing Checklist

- [ ] Category created with alias `comparison-matrix`
- [ ] Copy item created with brand name as title
- [ ] JSON is valid (test at jsonlint.com)
- [ ] Copy item linked to holiday
- [ ] Copy item state is Published
- [ ] View holiday page
- [ ] Navigate to Overview tab
- [ ] Comparison matrix displays
- [ ] Dropdown shows competitor name from JSON
- [ ] Selecting competitor populates column
- [ ] Icons display correctly
- [ ] Text is readable

## Quick Reference

| Item | Value |
|------|-------|
| Category Alias | `comparison-matrix` |
| Copy Item Title | Brand name (admin only) |
| Website Brand Name | From JSON `competitors.name` |
| Section Titles | Generic (reusable) |
| Feature Labels | Trip-specific |
| Feature Content | Trip-specific |

## Need Help?

See full documentation:
- `COMPARISON-MATRIX-SUMMARY.md` - Complete overview
- `COMPARISON-MATRIX-CHECKLIST.md` - Step-by-step checklist
- `templates/zenbase/html/com_zenholidays/holiday/COMPARISON-MATRIX-README.md` - Setup guide
- `templates/zenbase/html/com_zenholidays/holiday/comparison-matrix-sample.json` - Sample data

You're all set! 🚀

