Hi <PERSON>,

We've sent an invoice for 50% of the cost. If that can be paid ASAP that would be great.

We are getting started. There are a few steps, I've added <PERSON> onto the email to keep him in the loop.

1. Pre requisite updates to the booking journey
2. Test SKU migration
3. Update and test the booking journey & my account
4. Live SKU migration
5. Update website to use new SKUs (Jon)
6. Set and input USD prices into Joomla (Evertrek)

1. We've already done number 1. There were a few under the hood upgrades that had to be done to the booking app. Some of the libraries and frameworks we use in the booking app / members area would have soon become unsupported on the PaaS we use to host the front end of the booking apps. we've done 95% of those updates, the app has been through a round of testing and we're just making a few other changes.

2. We need to perform an update to the SKU (unique IDs) for of each of Evertrek's trips / products in RezKit to enable the multi-currency feature to work. This involves:
a). Swapping the SKUs against each product in RezKit, 
b). Changing the website to use the new SKU
c). and updating the SKUs on all Product Reservations in RezKit for all historical bookings

We've created a migration tool for this process and will run this and test this, on the staging version of RezKit in the next few days.

3. This happens after the test SKU migration. We're planning to do this part next week.

There are a couple of Evertrek support tickets outstanding, one relating to customers having an issue with file upload, another to do with an Alpkit link. We want to resolve those over the next few days before we do step 3 but thats fine because we have to do number 2 before we can do 3 anyway. 

4. This is just a rerun and test on live of step 2. But we have to do this at the same time as the website is updated which we'll coordinate with Jon. 

5. In terms of those website updates, there are a few steps for Jon:
a).Enable USD in the CMS (probably already done)
b) Enable IP lookup of location and set currency
c) Add a currency switcher to the website (if you want customers to be able to switch currency, if you only want a customers currency to be set based on their IP / location, then you don't need to do this).
d). Check USD prices displaying correctly
e). Update the link on the book now' buttons to use a new SKU. The website already has the data to do this, for Jon: I think off the top of my head it's a combination of price type ID and price ID which is already in the data for the departures, but we'll confirm this).

6. USD prices can then be loaded into the website and will then be bookable. USD prices can be loaded in advance of steps 4 and 5 if they are ready to do so. they can be loaded now even.

Any questions, please let me know.

Mark

----

Hi Jon,

New URL Structure
The main change we will need next week, will be to change the URL: parameters used when a customer clicks on the 'Book now' button for a departure date.

The URLs currently look like this:
http://secure.evertrek.co.uk/booking/?sku=holiday:1472

The last part of url (after 'holiday:' ) is what we call the price ID.

But in the future they will need to look like this:

http://secure.evertrek.co.uk/booking/?sku=holiday:1111-1&currency=GBP

The part after 'holiday:' is dateId-priceTypeId.
PriceTypeId will always be 1.
Currency should be either GBP or USD, but if this is missed the booking process will fallback to GBP.

I've added a USD Price on the test site to Everest Base camp Trek 27 March 2027 so you can see an example. All the data exists in the holiday object, it is just different data.

Currency you can set based on the users preference or IP lookup.

**SKU Update**
This is the change we have to make in RezKit to use the new IDs. We have to update product SKUs and then SKUs of products on existing bookings. 

We have already done a trial run of this on the staging version of RezKit.

**Updating the website & SKU update**
The URL structure needs to be updated on the website at the same time as we do the live RezKit SKU update on live.  I think we'll be ready to do this from next week.

USD prices and IP lookup don't need to be enabled at that point, that could be done later later. You could default everyone to GBP, use the new IDs and do the IP lookup when USD prices are ready and loaded into Joomla.

Happy to hop on a call if it helps!


-----

