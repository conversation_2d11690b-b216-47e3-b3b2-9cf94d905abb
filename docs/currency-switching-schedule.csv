Task ID,Section,Subsection,Task Name,Owner,Status,Priority,Estimated Hours,Jon Hours,Dependencies,Start Date,End Date,Notes,Deliverables,Time Notes,Automate?
CS-PREP-001,Preparation & Setup,CMS & Geolocation,Enable USD Currency in CMS,Jon,Not Started,Low,0.5,0.5,,2025-10-09,2025-10-09,"Verify USD is enabled in #__zencurrencies table; likely already done",USD currency active in system,"Phase 1: Preparation (3-4h)",
CS-PREP-002,Preparation & Setup,Geolocation,Implement IP-based Currency Detection,Jon,Not Started,High,1.5,1.5,CS-PREP-001,2025-10-09,2025-10-09,"Enhance geolocation plugin to set currency based on user IP; Not required at go-live (can default to GBP until USD is loaded)","Automatic USD for US visitors, GBP for others","Phase 1: Preparation (3-4h)",
CS-PREP-003,Preparation & Setup,UI,Add Currency Switcher UI,<PERSON>,Not Started,Medium,1.5,1.5,CS-PREP-002,2025-10-10,2025-10-10,"Enhance switcher module for visibility; style in template CSS; Optional (not required for go-live)","User-friendly currency switcher in header/footer","Phase 1: Preparation (3-4h)",
CS-CORE-002,Core URL Changes,Booking URL,Update Book Now Button URLs,Jon,Not Started,Critical,2.5,2.5,CS-PREP-003,2025-10-10,2025-10-10,"Change from holiday:priceId to holiday:dateId-priceTypeId&currency=XXX; PriceTypeId will always be 1","Updated booking URLs using new format","Phase 2: Core URL Changes (4-5h)",
CS-CORE-003,Core URL Changes,Booking URL,Add Currency Parameter to All Booking Links,Jon,Not Started,High,1,1,CS-CORE-002,2025-10-10,2025-10-10,"Ensure all booking entry points include currency parameter; If omitted, Rezkit falls back to GBP","Consistent currency parameter across all booking flows","Phase 2: Core URL Changes (4-5h)",
CS-CORE-004,Core URL Changes,API,Update ZenProvider API for Multi-Currency,Jon,Not Started,Medium,1,1,CS-CORE-003,2025-10-10,2025-10-10,"Verify `bookMultiCurrencyHoliday` builds `sku=holiday:{dateId}-1` (priceTypeId=1); append `&currency={GBP|USD}` (default GBP); validate `dateId`; allow-list currency (GBP,USD); handle missing/invalid safely; optional legacy `holiday:{priceId}` handling","Confirmed API compatibility","Phase 2: Core URL Changes (4-5h)",
CS-CORE-001,Price Display & Validation,Data,Load and Input USD Prices,Evertrek Team,Not Started,High,1,0,,2025-10-10,2025-10-10,"Coordinate with Evertrek team to load USD prices into Joomla","USD prices available in system","Phase 3: Price Display & Validation (3-4h)",
CS-VALID-001,Price Display & Validation,Display,Verify USD Price Display,Jon,Not Started,High,1.5,1.5,CS-CORE-001,2025-10-10,2025-10-10,"Test $/£ symbols and payment plan calculations in both currencies","Accurate price display in both currencies","Phase 3: Price Display & Validation (3-4h)",
CS-TEST-001,Testing & Go-Live,Functional,Test Currency Switching,Jon,Not Started,High,1,1,"CS-PREP-003;CS-CORE-002;CS-CORE-003",2025-10-10,2025-10-10,"End-to-end: IP detection, manual switch, session persistence, booking URLs","Fully functional currency switching","Phase 3: Price Display & Validation (3-4h)",
CS-TEST-002,Testing & Go-Live,QA,Pre-Go-Live Testing,Jon,Not Started,High,1,1,"CS-TEST-001;CS-CORE-004",2025-10-10,2025-10-10,"Comprehensive testing on staging before coordination","Tested and ready code","Phase 4: Testing & Go-Live (2h)",
CS-COMM-001,Testing & Go-Live,Coordination,Establish Go-Live Timing with Mark,Jon,Not Started,High,0.5,0.5,,2025-10-10,2025-10-10,"Agree window for coordinated deployment and Rezkit live SKU migration","Confirmed go-live window","Phase 4: Testing & Go-Live (2h)",
CS-GOLIVE-001,Testing & Go-Live,Deployment,Coordinated Go-Live,Mark (Rezkit),Not Started,Critical,1,0,"CS-DEP-004;CS-TEST-002",TBD,TBD,"Deploy URL changes simultaneously with Rezkit's live SKU migration; verify booking flow; monitor","Live multi-currency system","Phase 4: Testing & Go-Live (2h)",
CS-DEP-001,Dependencies (Rezkit),Booking Journey,Pre-requisite updates to booking journey,Mark (Rezkit),In Progress,Medium,,0,,TBD,TBD,"95% complete per Mark","","Dependencies with Rezkit",
CS-DEP-002,Dependencies (Rezkit),SKU Migration,Test SKU migration,Mark (Rezkit),In Progress,High,,0,CS-DEP-001,TBD,TBD,"Rezkit test migration on staging","","Dependencies with Rezkit",
CS-DEP-003,Dependencies (Rezkit),Support,Outstanding support tickets resolution,Mark (Rezkit),Not Started,Medium,,0,,TBD,TBD,"File upload & Alpkit link outstanding","","Dependencies with Rezkit",
CS-DEP-004,Dependencies (Rezkit),SKU Migration,Live SKU migration,Mark (Rezkit),Not Started,Critical,,0,"CS-DEP-002;CS-DEP-003;CS-DEP-005;CS-CORE-003",TBD,TBD,"Must happen simultaneously with website URL structure update","","Dependencies with Rezkit",
CS-DEP-005,Dependencies (Rezkit),Booking Journey,Update and test the booking journey & my account,Mark (Rezkit),Not Started,High,,0,CS-DEP-002,TBD,TBD,"Rezkit step 3 (next week per notes)","","Dependencies with Rezkit",
