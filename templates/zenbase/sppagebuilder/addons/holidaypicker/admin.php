<?php

use Joomla\CMS\Factory;
use <PERSON><PERSON><PERSON>\CMS\Language\Text;

// No direct access
defined('_JEXEC') or die('Restricted access');

// Get holidays organized by continent for React-friendly filtering
$db = Factory::getDbo();

// Get all holidays with their continent information
$query = $db->getQuery(true)
    ->select('DISTINCT h.id, h.name as title, h.featured, l.id as continent_id, l.name as continent_name')
    ->from('#__zenholidays h')
    ->join('LEFT', '#__zenlocations_usage lu ON lu.item_id = h.id AND lu.extension = ' . $db->quote('com_zenholidays') . ' AND lu.item_type = ' . $db->quote('holiday'))
    ->join('LEFT', '#__zenlocations l ON (l.id = lu.location_id AND l.level = 1) OR (l.id = (SELECT parent_id FROM #__zenlocations WHERE id = lu.location_id AND level = 2))')
    ->where('h.state = 1')
    ->where('(l.id IS NULL OR (l.level = 1 AND l.state = 1 AND l.name IN (' . $db->quote('Africa') . ', ' . $db->quote('Asia') . ', ' . $db->quote('Europe') . ', ' . $db->quote('South America') . ', ' . $db->quote('North America') . ', ' . $db->quote('Oceania') . ', ' . $db->quote('Antarctica') . ')))')
    ->order('h.name ASC');

$db->setQuery($query);
$holidaysWithContinents = $db->loadObjectList();

// Organize holidays by continent
$holidaysByContinent = array();
$allHolidays = array();

foreach ($holidaysWithContinents as $holiday) {
    $label = $holiday->title;
    if ($holiday->featured == 1) {
        $label = '⭐ ' . $label;
    }

    // Add to all holidays
    $allHolidays[$holiday->id] = $label;

    // Add to continent-specific array
    if ($holiday->continent_id && $holiday->continent_name) {
        if (!isset($holidaysByContinent[$holiday->continent_id])) {
            $holidaysByContinent[$holiday->continent_id] = array();
        }
        $holidaysByContinent[$holiday->continent_id][$holiday->id] = $label;
    }
}

// Create holiday values arrays for each continent
$allHolidayValues = array('' => Text::_('Select a Holiday')) + $allHolidays;

// Get continents for filtering - using zenlocations table (level 1 = continents)
// Filter out countries that are incorrectly marked as level 1
$query = $db->getQuery(true)
    ->select('id, name as title')
    ->from('#__zenlocations')
    ->where('level = 1')
    ->where('state = 1')
    ->where('name IN (' . $db->quote('Africa') . ', ' . $db->quote('Asia') . ', ' . $db->quote('Europe') . ', ' . $db->quote('South America') . ', ' . $db->quote('North America') . ', ' . $db->quote('Oceania') . ', ' . $db->quote('Antarctica') . ')')
    ->order('name ASC');

$db->setQuery($query);
$continents = $db->loadObjectList();

$continentValues = array();
$continentValues[''] = Text::_('All Continents');
if (!empty($continents)) {
    foreach ($continents as $continent) {
        $continentValues[$continent->id] = $continent->title;
    }
}

// Order by options
$orderByValues = array(
    'name_asc' => Text::_('Name (A-Z)'),
    'name_desc' => Text::_('Name (Z-A)'),
    'featured_first' => Text::_('Featured First'),
    'id_asc' => Text::_('Oldest First'),
    'id_desc' => Text::_('Newest First')
);

// Display style options
$displayStyles = array(
    'card' => Text::_('Card Style'),
    'minimal' => Text::_('Minimal Style'),
    'detailed' => Text::_('Detailed Style'),
    'list' => Text::_('List Style')
);

// Create continent-specific holiday fields
$continentHolidayFields = array();
foreach ($continents as $continent) {
    $continentHolidayValues = array('' => Text::_('Select a Holiday'));
    if (isset($holidaysByContinent[$continent->id])) {
        $continentHolidayValues = $continentHolidayValues + $holidaysByContinent[$continent->id];
    }

    $fieldName = 'holiday_ids_' . $continent->id;
    $continentHolidayFields[$fieldName] = array(
        'type' => 'select',
        'title' => Text::_('Select Holidays') . ' (' . $continent->title . ')',
        'desc' => Text::_('Choose holidays to display from ' . $continent->title . '. Hold Ctrl/Cmd to select multiple. Featured holidays are marked with ⭐'),
        'values' => $continentHolidayValues,
        'multiple' => true,
        'size' => 8,
        'std' => array(),
        'depends' => array(
            array('continent_filter', '=', $continent->id)
        )
    );
}

SpAddonsConfig::addonConfig(
    array(
        'type' => 'content',
        'addon_name' => 'holidaypicker',
        'title' => Text::_('Enhanced Holiday Picker'),
        'desc' => Text::_('Select and display holidays with improved admin interface'),
        'category' => 'Content',
        'attr' => array(
            'general' => array(
                'admin_label' => array(
                    'type' => 'text',
                    'title' => Text::_('COM_SPPAGEBUILDER_ADDON_ADMIN_LABEL'),
                    'desc' => Text::_('COM_SPPAGEBUILDER_ADDON_ADMIN_LABEL_DESC'),
                    'std' => ''
                ),
                'title' => array(
                    'type' => 'text',
                    'title' => Text::_('COM_SPPAGEBUILDER_ADDON_TITLE'),
                    'desc' => Text::_('COM_SPPAGEBUILDER_ADDON_TITLE_DESC'),
                    'std' => ''
                ),
                'heading_selector' => array(
                    'type' => 'select',
                    'title' => Text::_('COM_SPPAGEBUILDER_ADDON_HEADINGS'),
                    'desc' => Text::_('COM_SPPAGEBUILDER_ADDON_HEADINGS_DESC'),
                    'values' => array(
                        'h1' => Text::_('COM_SPPAGEBUILDER_ADDON_HEADINGS_H1'),
                        'h2' => Text::_('COM_SPPAGEBUILDER_ADDON_HEADINGS_H2'),
                        'h3' => Text::_('COM_SPPAGEBUILDER_ADDON_HEADINGS_H3'),
                        'h4' => Text::_('COM_SPPAGEBUILDER_ADDON_HEADINGS_H4'),
                        'h5' => Text::_('COM_SPPAGEBUILDER_ADDON_HEADINGS_H5'),
                        'h6' => Text::_('COM_SPPAGEBUILDER_ADDON_HEADINGS_H6'),
                    ),
                    'std' => 'h3',
                    'depends' => array(array('title', '!=', '')),
                ),

                'separator_selection' => array(
                    'type' => 'separator',
                    'title' => Text::_('Holiday Selection')
                ),

                'continent_filter' => array(
                    'type' => 'select',
                    'title' => Text::_('Filter by Continent'),
                    'desc' => Text::_('Filter holidays by continent to narrow down choices'),
                    'values' => $continentValues,
                    'std' => ''
                ),

                'holiday_ids_all' => array(
                    'type' => 'select',
                    'title' => Text::_('Select Holidays'),
                    'desc' => Text::_('Choose holidays to display'),
                    'values' => $allHolidays, // Remove the placeholder option
                    'multiple' => true,
                    'size' => 8,
                    'std' => array(),
                    'depends' => array(
                        array('continent_filter', '=', '')
                    )
                ),

                // Africa holidays (continent_id = 4)
                'holiday_ids_4' => array(
                    'type' => 'select',
                    'title' => Text::_('Select Holidays (Africa)'),
                    'desc' => Text::_('Choose holidays to display from Africa'),
                    'values' => isset($holidaysByContinent[4]) ? $holidaysByContinent[4] : array(),
                    'multiple' => true,
                    'size' => 8,
                    'std' => array(),
                    'depends' => array(
                        array('continent_filter', '=', '4')
                    )
                ),

                // Asia holidays (continent_id = 2)
                'holiday_ids_2' => array(
                    'type' => 'select',
                    'title' => Text::_('Select Holidays (Asia)'),
                    'desc' => Text::_('Choose holidays to display from Asia'),
                    'values' => isset($holidaysByContinent[2]) ? $holidaysByContinent[2] : array(),
                    'multiple' => true,
                    'size' => 8,
                    'std' => array(),
                    'depends' => array(
                        array('continent_filter', '=', '2')
                    )
                ),

                // Europe holidays (continent_id = 3)
                'holiday_ids_3' => array(
                    'type' => 'select',
                    'title' => Text::_('Select Holidays (Europe)'),
                    'desc' => Text::_('Choose holidays to display from Europe'),
                    'values' => isset($holidaysByContinent[3]) ? $holidaysByContinent[3] : array(),
                    'multiple' => true,
                    'size' => 8,
                    'std' => array(),
                    'depends' => array(
                        array('continent_filter', '=', '3')
                    )
                ),

                // South America holidays (continent_id = 5)
                'holiday_ids_5' => array(
                    'type' => 'select',
                    'title' => Text::_('Select Holidays (South America)'),
                    'desc' => Text::_('Choose holidays to display from South America'),
                    'values' => isset($holidaysByContinent[5]) ? $holidaysByContinent[5] : array(),
                    'multiple' => true,
                    'size' => 8,
                    'std' => array(),
                    'depends' => array(
                        array('continent_filter', '=', '5')
                    )
                ),

                'order_by' => array(
                    'type' => 'select',
                    'title' => Text::_('Order By'),
                    'desc' => Text::_('Choose how to order the selected holidays'),
                    'values' => $orderByValues,
                    'std' => 'featured_first'
                ),

                'separator_display' => array(
                    'type' => 'separator',
                    'title' => Text::_('Display Options')
                ),

                'display_style' => array(
                    'type' => 'select',
                    'title' => Text::_('Display Style'),
                    'desc' => Text::_('Choose how the holiday should be displayed'),
                    'values' => $displayStyles,
                    'std' => 'card'
                ),

                'show_dates' => array(
                    'type' => 'checkbox',
                    'title' => Text::_('Show Available Dates'),
                    'desc' => Text::_('Display upcoming available dates for the holiday'),
                    'std' => 1
                ),

                'show_pricing' => array(
                    'type' => 'checkbox',
                    'title' => Text::_('Show Pricing'),
                    'desc' => Text::_('Display pricing information if available'),
                    'std' => 0
                ),

                'limit_dates' => array(
                    'type' => 'number',
                    'title' => Text::_('Limit Dates'),
                    'desc' => Text::_('Maximum number of dates to show (0 = no limit)'),
                    'std' => 3,
                    'depends' => array(array('show_dates', '=', 1))
                ),

                'class' => array(
                    'type' => 'text',
                    'title' => Text::_('COM_SPPAGEBUILDER_ADDON_CLASS'),
                    'desc' => Text::_('COM_SPPAGEBUILDER_ADDON_CLASS_DESC'),
                    'std' => ''
                ),
            ),
        ),
    )
);

// No JavaScript needed - using SP Page Builder's React-based field dependencies
