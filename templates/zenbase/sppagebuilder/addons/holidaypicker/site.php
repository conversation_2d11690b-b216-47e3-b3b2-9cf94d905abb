<?php

use <PERSON><PERSON><PERSON>\CMS\Factory;
use <PERSON><PERSON><PERSON>\CMS\Language\Text;

// No direct access
defined('_JEXEC') or die('Restricted access');

class SppagebuilderAddonHolidaypicker extends SppagebuilderAddons
{
    public function render()
    {
        $class = (isset($this->addon->settings->class) && $this->addon->settings->class) ? $this->addon->settings->class : '';
        $title = (isset($this->addon->settings->title) && $this->addon->settings->title) ? $this->addon->settings->title : '';
        $heading_selector = (isset($this->addon->settings->heading_selector) && $this->addon->settings->heading_selector) ? $this->addon->settings->heading_selector : 'h3';
        
        // Collect holiday IDs from all possible fields (all continents + continent-specific)
        $holiday_ids_all = (isset($this->addon->settings->holiday_ids_all) && $this->addon->settings->holiday_ids_all) ? $this->addon->settings->holiday_ids_all : array();
        $holiday_ids_2 = (isset($this->addon->settings->holiday_ids_2) && $this->addon->settings->holiday_ids_2) ? $this->addon->settings->holiday_ids_2 : array(); // Asia
        $holiday_ids_3 = (isset($this->addon->settings->holiday_ids_3) && $this->addon->settings->holiday_ids_3) ? $this->addon->settings->holiday_ids_3 : array(); // Europe
        $holiday_ids_4 = (isset($this->addon->settings->holiday_ids_4) && $this->addon->settings->holiday_ids_4) ? $this->addon->settings->holiday_ids_4 : array(); // Africa
        $holiday_ids_5 = (isset($this->addon->settings->holiday_ids_5) && $this->addon->settings->holiday_ids_5) ? $this->addon->settings->holiday_ids_5 : array(); // South America

        $order_by = (isset($this->addon->settings->order_by) && $this->addon->settings->order_by) ? $this->addon->settings->order_by : 'featured_first';

        $display_style = (isset($this->addon->settings->display_style) && $this->addon->settings->display_style) ? $this->addon->settings->display_style : 'card';
        $show_dates = (isset($this->addon->settings->show_dates) && $this->addon->settings->show_dates) ? $this->addon->settings->show_dates : 1;
        $show_pricing = (isset($this->addon->settings->show_pricing) && $this->addon->settings->show_pricing) ? $this->addon->settings->show_pricing : 0;
        $limit_dates = (isset($this->addon->settings->limit_dates) && $this->addon->settings->limit_dates) ? (int)$this->addon->settings->limit_dates : 3;

        // Determine which holidays to load - merge all selected holidays from different fields
        $holidayIds = array();

        // Collect from all continent-specific fields
        $allHolidayFields = array($holiday_ids_all, $holiday_ids_2, $holiday_ids_3, $holiday_ids_4, $holiday_ids_5);
        foreach ($allHolidayFields as $fieldHolidays) {
            if (!empty($fieldHolidays) && is_array($fieldHolidays)) {
                $validIds = array_filter(array_map('intval', $fieldHolidays));
                $holidayIds = array_merge($holidayIds, $validIds);
            }
        }

        // Remove duplicates
        $holidayIds = array_unique($holidayIds);

        if (empty($holidayIds)) {
            return '<div class="alert alert-warning">' . Text::_('Please select at least one holiday to display.') . '</div>';
        }

        // Get holidays data
        $holidays = $this->getHolidays($holidayIds, $order_by);
        
        if (empty($holidays)) {
            return '<div class="alert alert-info">' . Text::_('No holidays found for the selected criteria.') . '</div>';
        }

        $output = '<div class="sppb-addon sppb-addon-holidaypicker ' . $class . '">';
        $output .= '<div class="sppb-addon-content">';
        
        if ($title) {
            $output .= '<' . $heading_selector . ' class="sppb-addon-title">' . $title . '</' . $heading_selector . '>';
        }

        $output .= '<div class="holidaypicker-container holidaypicker-style-' . $display_style . '">';
        
        if (count($holidays) > 1) {
            $output .= '<div class="holidaypicker-grid">';
        }

        foreach ($holidays as $holiday) {
            $output .= $this->renderHoliday($holiday, $display_style, $show_dates, $show_pricing, $limit_dates);
        }
        
        if (count($holidays) > 1) {
            $output .= '</div>';
        }

        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        return $output;
    }

    private function getHolidays($holidayIds, $order_by = 'featured_first')
    {
        $db = Factory::getDbo();
        $query = $db->getQuery(true);

        $query->select('h.id, h.title, h.name, h.alias, h.intro, h.description, h.featured')
            ->from('#__zenholidays h')
            ->where('h.state = 1')
            ->where('(h.publish_up = ' . $db->quote('0000-00-00 00:00:00') . ' OR h.publish_up <= NOW())')
            ->where('(h.publish_down = ' . $db->quote('0000-00-00 00:00:00') . ' OR h.publish_down >= NOW())')
            ->where('h.id IN (' . implode(',', array_map('intval', $holidayIds)) . ')');

        // Apply ordering based on order_by parameter
        switch ($order_by) {
            case 'name_asc':
                $query->order('h.name ASC');
                break;
            case 'name_desc':
                $query->order('h.name DESC');
                break;
            case 'featured_first':
                $query->order('h.featured DESC, h.name ASC');
                break;
            case 'id_asc':
                $query->order('h.id ASC');
                break;
            case 'id_desc':
                $query->order('h.id DESC');
                break;
            default:
                $query->order('h.featured DESC, h.name ASC');
                break;
        }

        $db->setQuery($query);
        $holidays = $db->loadObjectList();

        // Load additional data for each holiday
        foreach ($holidays as &$holiday) {
            if ($holiday) {
                // Get holiday dates if needed
                $holiday->dates = $this->getHolidayDates($holiday->id);
                
                // Get basic pricing info if needed
                $holiday->pricing = $this->getHolidayPricing($holiday->id);
            }
        }

        return $holidays;
    }

    private function getHolidayDates($holidayId, $limit = 5)
    {
        $db = Factory::getDbo();
        $query = $db->getQuery(true);

        $query->select('hd.id, hd.start_date, hd.end_date, hd.spaces, hd.capacity, hd.status')
            ->from('#__zenholidaydates hd')
            ->where('hd.holiday_id = ' . (int)$holidayId)
            ->where('hd.state = 1')
            ->where('hd.start_date >= CURDATE()')
            ->order('hd.start_date ASC');
            
        if ($limit > 0) {
            $query->setLimit($limit);
        }

        $db->setQuery($query);
        return $db->loadObjectList();
    }

    private function getHolidayPricing($holidayId)
    {
        $db = Factory::getDbo();
        $query = $db->getQuery(true);

        // Get basic pricing info - using correct column name 'value'
        $query->select('MIN(hp.value) as min_price, MAX(hp.value) as max_price, hp.currency_code')
            ->from('#__zenholidayprices hp')
            ->join('INNER', '#__zenholidaydates hd ON hd.id = hp.date_id')
            ->join('INNER', '#__zenholidaypricetypes pt ON pt.id = hp.type_id')
            ->where('hd.holiday_id = ' . (int)$holidayId)
            ->where('hd.state = 1')
            ->where('hd.start_date >= CURDATE()')
            ->where('pt.is_from_price = 1')
            ->where('hp.value > 0')
            ->group('hp.currency_code')
            ->order('hp.value ASC');

        $db->setQuery($query);
        return $db->loadObject();
    }

    private function renderHoliday($holiday, $display_style, $show_dates, $show_pricing, $limit_dates)
    {
        $output = '<div class="holidaypicker-item holidaypicker-item-' . $display_style . '">';
        
        // Use existing holiday template if available
        $app = Factory::getApplication();
        $template = JPATH_ROOT . '/templates/' . $app->getTemplate() . '/sppagebuilder/addons/holiday/tmpl.php';

        if (file_exists($template)) {
            // Use existing template
            ob_start();
            include($template);
            $output .= ob_get_contents();
            ob_end_clean();
        } else {
            // Fallback rendering
            $output .= $this->renderHolidayFallback($holiday, $display_style, $show_dates, $show_pricing, $limit_dates);
        }

        $output .= '</div>';
        return $output;
    }

    private function renderHolidayFallback($holiday, $display_style, $show_dates, $show_pricing, $limit_dates)
    {
        $output = '<div class="holidaypicker-fallback">';
        
        if ($holiday->featured) {
            $output .= '<div class="holidaypicker-featured">⭐ Featured</div>';
        }
        
        $output .= '<h4 class="holidaypicker-title">' . htmlspecialchars($holiday->title) . '</h4>';
        
        if (!empty($holiday->intro)) {
            $output .= '<div class="holidaypicker-intro">' . htmlspecialchars(substr($holiday->intro, 0, 150)) . '...</div>';
        }
        
        if ($show_dates && !empty($holiday->dates)) {
            $output .= '<div class="holidaypicker-dates">';
            $output .= '<h5>Available Dates:</h5>';
            $count = 0;
            foreach ($holiday->dates as $date) {
                if ($limit_dates > 0 && $count >= $limit_dates) break;
                $output .= '<div class="holidaypicker-date">';
                $output .= date('M j, Y', strtotime($date->start_date));
                if ($date->end_date && $date->end_date != $date->start_date) {
                    $output .= ' - ' . date('M j, Y', strtotime($date->end_date));
                }
                if ($date->spaces && $date->capacity) {
                    $output .= ' (' . $date->spaces . '/' . $date->capacity . ' spaces)';
                }
                $output .= '</div>';
                $count++;
            }
            $output .= '</div>';
        }
        
        if ($show_pricing && !empty($holiday->pricing) && $holiday->pricing->min_price) {
            $output .= '<div class="holidaypicker-pricing">';
            if ($holiday->pricing->min_price == $holiday->pricing->max_price) {
                $output .= '<span class="price">£' . number_format($holiday->pricing->min_price) . '</span>';
            } else {
                $output .= '<span class="price">£' . number_format($holiday->pricing->min_price) . ' - £' . number_format($holiday->pricing->max_price) . '</span>';
            }
            $output .= '</div>';
        }
        
        $output .= '<div class="holidaypicker-actions">';
        $output .= '<a href="/holidays/view?id=' . $holiday->id . '" class="btn btn-primary">View Details</a>';
        $output .= '</div>';
        
        $output .= '</div>';
        return $output;
    }

    /**
     * AJAX method to get filtered holidays based on continent selection
     */
    public static function getFilteredHolidaysAjax()
    {
        $input = Factory::getApplication()->input;
        $continent_filter = $input->getInt('continent_filter', 0);

        try {
            $db = Factory::getDbo();

            $query = $db->getQuery(true)
                ->select('DISTINCT h.id, h.name as title, h.featured')
                ->from('#__zenholidays h')
                ->where('h.state = 1');

            // If continent filter is applied, join with locations
            if ($continent_filter > 0) {
                // First verify the continent_filter is a valid continent
                $continentCheck = $db->getQuery(true)
                    ->select('COUNT(*)')
                    ->from('#__zenlocations')
                    ->where('id = ' . (int)$continent_filter)
                    ->where('level = 1')
                    ->where('state = 1')
                    ->where('name IN (' . $db->quote('Africa') . ', ' . $db->quote('Asia') . ', ' . $db->quote('Europe') . ', ' . $db->quote('South America') . ', ' . $db->quote('North America') . ', ' . $db->quote('Oceania') . ', ' . $db->quote('Antarctica') . ')');

                $db->setQuery($continentCheck);
                $isValidContinent = $db->loadResult();

                if ($isValidContinent) {
                    $query->join('INNER', '#__zenlocations_usage lu ON lu.item_id = h.id')
                        ->join('INNER', '#__zenlocations l ON l.id = lu.location_id')
                        ->where('lu.extension = ' . $db->quote('com_zenholidays'))
                        ->where('lu.item_type = ' . $db->quote('holiday'))
                        ->where('(l.id = ' . (int)$continent_filter . ' OR l.parent_id = ' . (int)$continent_filter . ' OR l.path LIKE ' . $db->quote('%/' . (int)$continent_filter . '/%') . ')');
                }
            }

            $query->order('h.name ASC');

            $db->setQuery($query);
            $holidays = $db->loadObjectList();

            $holidayValues = array();
            $holidayValues[''] = Text::_('Select a Holiday');

            if (!empty($holidays)) {
                foreach ($holidays as $holiday) {
                    $label = $holiday->title;
                    // Add star for featured holidays (featured = 1)
                    if ($holiday->featured == 1) {
                        $label = '⭐ ' . $label;
                    }
                    $holidayValues[$holiday->id] = $label;
                }
            }

            return [
                'success' => true,
                'data' => $holidayValues,
                'message' => 'Holidays loaded successfully'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'data' => null,
                'message' => 'Error loading holidays: ' . $e->getMessage()
            ];
        }
    }
}
